<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Assessment Parser</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Assessment Parser Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Phase 1: Big Five</h2>
                <div id="phase1-info"></div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Phase 2: RIASEC</h2>
                <div id="phase2-info"></div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Phase 3: VIA</h2>
                <div id="phase3-info"></div>
            </div>
        </div>
        
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Sample Questions</h2>
            <div id="sample-questions"></div>
        </div>
        
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Likert Scale</h2>
            <div id="likert-scale"></div>
        </div>
    </div>

    <script type="module">
        // Assessment Questions Parser
        class AssessmentParser {
            constructor() {
                this.phases = {
                    1: {
                        name: "Big Five Personality",
                        description: "Mengukur lima dimensi utama kepribadian Anda",
                        file: "Big Five Inventory (BFI-44) Self-Assessment.txt",
                        categories: ["Openness to Experience", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"]
                    },
                    2: {
                        name: "RIASEC Holland Codes", 
                        description: "Mengidentifikasi minat karir dan lingkungan kerja yang sesuai",
                        file: "RIASEC Holland Codes Self-Assessment.txt",
                        categories: ["Realistic", "Investigative", "Artistic", "Social", "Enterprising", "Conventional"]
                    },
                    3: {
                        name: "VIA Character Strengths",
                        description: "Mengenali kekuatan karakter dan nilai-nilai inti Anda",
                        file: "VIA Character Strengths Self-Assessment.txt", 
                        categories: ["Wisdom", "Courage", "Humanity", "Justice", "Temperance", "Transcendence"]
                    }
                };
            }

            parseBigFiveQuestions() {
                const questions = [];
                let questionId = 1;

                const opennessQuestions = [
                    "Is original, comes up with new ideas",
                    "Is curious about many different things", 
                    "Is ingenious, a deep thinker"
                ];

                opennessQuestions.forEach((text, index) => {
                    questions.push({
                        id: questionId++,
                        phase: 1,
                        category: "Openness to Experience",
                        text: `Saya adalah seseorang yang ${text.toLowerCase()}`,
                        isReverse: false,
                        originalText: text
                    });
                });

                return questions;
            }

            parseRiasecQuestions() {
                const questions = [];
                let questionId = 1000;

                const realisticQuestions = [
                    "I enjoy working with my hands, using tools, or operating machinery",
                    "Building, repairing, or fixing things is satisfying to me",
                    "I prefer working outdoors rather than in an office"
                ];

                realisticQuestions.forEach((text, index) => {
                    questions.push({
                        id: questionId++,
                        phase: 2,
                        category: "Realistic",
                        text: text,
                        originalText: text
                    });
                });

                return questions;
            }

            parseViaQuestions() {
                const questions = [];
                let questionId = 2000;

                const wisdomQuestions = [
                    "I am always coming up with new ways to do things",
                    "Thinking of novel ideas is one of my greatest strengths",
                    "I am an original thinker"
                ];

                wisdomQuestions.forEach((text, index) => {
                    questions.push({
                        id: questionId++,
                        phase: 3,
                        category: "Wisdom",
                        text: text,
                        originalText: text
                    });
                });

                return questions;
            }

            getPhaseQuestions(phaseNumber) {
                switch(phaseNumber) {
                    case 1:
                        return this.parseBigFiveQuestions();
                    case 2:
                        return this.parseRiasecQuestions();
                    case 3:
                        return this.parseViaQuestions();
                    default:
                        return [];
                }
            }

            getPhaseInfo(phaseNumber) {
                return this.phases[phaseNumber] || null;
            }

            getAllPhases() {
                return this.phases;
            }

            getLikertScale() {
                return [
                    { value: 1, label: "Sangat Tidak Setuju", shortLabel: "1" },
                    { value: 2, label: "Tidak Setuju", shortLabel: "2" },
                    { value: 3, label: "Agak Tidak Setuju", shortLabel: "3" },
                    { value: 4, label: "Netral", shortLabel: "4" },
                    { value: 5, label: "Agak Setuju", shortLabel: "5" },
                    { value: 6, label: "Setuju", shortLabel: "6" },
                    { value: 7, label: "Sangat Setuju", shortLabel: "7" }
                ];
            }
        }

        // Test the parser
        const parser = new AssessmentParser();
        
        // Display phase information
        for (let i = 1; i <= 3; i++) {
            const phaseInfo = parser.getPhaseInfo(i);
            const questions = parser.getPhaseQuestions(i);
            
            document.getElementById(`phase${i}-info`).innerHTML = `
                <p class="text-sm text-gray-600 mb-2">${phaseInfo.description}</p>
                <p class="text-sm"><strong>Categories:</strong> ${phaseInfo.categories.length}</p>
                <p class="text-sm"><strong>Questions:</strong> ${questions.length}</p>
                <p class="text-sm"><strong>ID Range:</strong> ${questions[0]?.id} - ${questions[questions.length-1]?.id}</p>
            `;
        }
        
        // Display sample questions
        const sampleQuestions = [
            ...parser.getPhaseQuestions(1).slice(0, 2),
            ...parser.getPhaseQuestions(2).slice(0, 2),
            ...parser.getPhaseQuestions(3).slice(0, 2)
        ];
        
        document.getElementById('sample-questions').innerHTML = sampleQuestions.map(q => `
            <div class="mb-4 p-4 border border-gray-200 rounded">
                <div class="flex items-center mb-2">
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full mr-2">
                        Phase ${q.phase}
                    </span>
                    <span class="text-sm text-gray-500">${q.category}</span>
                </div>
                <p class="text-gray-900">${q.text}</p>
                <p class="text-xs text-gray-500 mt-1">ID: ${q.id}</p>
            </div>
        `).join('');
        
        // Display Likert scale
        const likertScale = parser.getLikertScale();
        document.getElementById('likert-scale').innerHTML = `
            <div class="grid grid-cols-7 gap-2">
                ${likertScale.map(option => `
                    <div class="text-center p-3 border border-gray-200 rounded">
                        <div class="text-lg font-bold mb-1">${option.value}</div>
                        <div class="text-xs text-gray-600">${option.label}</div>
                    </div>
                `).join('')}
            </div>
        `;
        
        console.log('Assessment Parser Test Complete');
        console.log('Phase 1 Questions:', parser.getPhaseQuestions(1).length);
        console.log('Phase 2 Questions:', parser.getPhaseQuestions(2).length);
        console.log('Phase 3 Questions:', parser.getPhaseQuestions(3).length);
    </script>
</body>
</html>
