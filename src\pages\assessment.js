export function createAssessmentPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('dashboard')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← Kembali
              </button>
              <h1 class="text-xl font-semibold text-gray-900">Assessment Talenta</h1>
            </div>
            <div class="flex items-center">
              <span class="text-sm text-gray-500">Pertanyaan <span id="current-question">1</span> dari <span id="total-questions">10</span></span>
            </div>
          </div>
        </div>
      </nav>

      <!-- Progress Bar -->
      <div class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="py-4">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div id="progress-bar" class="bg-indigo-600 h-2 rounded-full transition-all duration-300" style="width: 10%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Assessment Content -->
      <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow rounded-lg p-8">
          <div id="assessment-content">
            <!-- Question will be loaded here -->
            <div class="mb-8">
              <h2 class="text-xl font-semibold text-gray-900 mb-4" id="question-title">
                Bagaimana Anda biasanya menangani situasi yang penuh tekanan?
              </h2>
              <p class="text-gray-600 mb-6" id="question-description">
                Pilih jawaban yang paling menggambarkan diri Anda.
              </p>
            </div>

            <div class="space-y-4" id="question-options">
              <label class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input type="radio" name="answer" value="a" class="mt-1 mr-3">
                <span class="text-gray-900">Saya tetap tenang dan mencari solusi secara sistematis</span>
              </label>
              <label class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input type="radio" name="answer" value="b" class="mt-1 mr-3">
                <span class="text-gray-900">Saya meminta bantuan dari rekan kerja atau atasan</span>
              </label>
              <label class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input type="radio" name="answer" value="c" class="mt-1 mr-3">
                <span class="text-gray-900">Saya bekerja lebih keras dan lebih lama untuk menyelesaikan masalah</span>
              </label>
              <label class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input type="radio" name="answer" value="d" class="mt-1 mr-3">
                <span class="text-gray-900">Saya mengambil waktu sejenak untuk menenangkan diri sebelum bertindak</span>
              </label>
            </div>

            <div class="flex justify-between mt-8">
              <button id="prev-btn" onclick="previousQuestion()" 
                class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled>
                Sebelumnya
              </button>
              <button id="next-btn" onclick="nextQuestion()" 
                class="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed">
                Selanjutnya
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Sample questions data
const assessmentQuestions = [
  {
    id: 1,
    title: "Bagaimana Anda biasanya menangani situasi yang penuh tekanan?",
    description: "Pilih jawaban yang paling menggambarkan diri Anda.",
    options: [
      { value: "a", text: "Saya tetap tenang dan mencari solusi secara sistematis" },
      { value: "b", text: "Saya meminta bantuan dari rekan kerja atau atasan" },
      { value: "c", text: "Saya bekerja lebih keras dan lebih lama untuk menyelesaikan masalah" },
      { value: "d", text: "Saya mengambil waktu sejenak untuk menenangkan diri sebelum bertindak" }
    ]
  },
  {
    id: 2,
    title: "Ketika bekerja dalam tim, peran apa yang paling sering Anda ambil?",
    description: "Pilih peran yang paling sesuai dengan kepribadian Anda.",
    options: [
      { value: "a", text: "Pemimpin yang mengarahkan dan mengkoordinasi" },
      { value: "b", text: "Mediator yang menjaga harmoni tim" },
      { value: "c", text: "Eksekutor yang fokus menyelesaikan tugas" },
      { value: "d", text: "Inovator yang memberikan ide-ide kreatif" }
    ]
  }
  // Add more questions as needed
];

let currentQuestionIndex = 0;
let answers = {};

export function loadQuestion(index) {
  const question = assessmentQuestions[index];
  if (!question) return;

  document.getElementById('question-title').textContent = question.title;
  document.getElementById('question-description').textContent = question.description;
  document.getElementById('current-question').textContent = index + 1;
  document.getElementById('total-questions').textContent = assessmentQuestions.length;
  
  // Update progress bar
  const progress = ((index + 1) / assessmentQuestions.length) * 100;
  document.getElementById('progress-bar').style.width = `${progress}%`;
  
  // Load options
  const optionsContainer = document.getElementById('question-options');
  optionsContainer.innerHTML = question.options.map(option => `
    <label class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
      <input type="radio" name="answer" value="${option.value}" class="mt-1 mr-3" ${answers[question.id] === option.value ? 'checked' : ''}>
      <span class="text-gray-900">${option.text}</span>
    </label>
  `).join('');
  
  // Update navigation buttons
  document.getElementById('prev-btn').disabled = index === 0;
  document.getElementById('next-btn').textContent = index === assessmentQuestions.length - 1 ? 'Selesai' : 'Selanjutnya';
}

export function nextQuestion() {
  const selectedAnswer = document.querySelector('input[name="answer"]:checked');
  if (!selectedAnswer) {
    alert('Silakan pilih jawaban terlebih dahulu');
    return;
  }
  
  // Save answer
  const currentQuestion = assessmentQuestions[currentQuestionIndex];
  answers[currentQuestion.id] = selectedAnswer.value;
  
  if (currentQuestionIndex < assessmentQuestions.length - 1) {
    currentQuestionIndex++;
    loadQuestion(currentQuestionIndex);
  } else {
    // Assessment completed
    submitAssessment();
  }
}

export function previousQuestion() {
  if (currentQuestionIndex > 0) {
    currentQuestionIndex--;
    loadQuestion(currentQuestionIndex);
  }
}

export function submitAssessment() {
  // Save answers to localStorage or send to server
  localStorage.setItem('assessmentAnswers', JSON.stringify(answers));
  localStorage.setItem('assessmentCompleted', 'true');
  
  // Navigate to waiting page
  navigateTo('waiting');
}

// Initialize assessment when page loads
export function initAssessment() {
  currentQuestionIndex = 0;
  answers = {};
  loadQuestion(0);
}
